"use client";

import { memo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Heart, MapPin, Clock } from "lucide-react";
import type { Job } from "@/types/job";
import { cn } from "@/lib/utils";

interface JobCardProps {
  job: Job;
  className?: string;
  onBookmark?: (jobId: string) => void;
  onApply?: (jobId: string) => void;
  onViewDetails?: (jobId: string) => void;
  variant?: "grid" | "list";
  isMobile?: boolean;
}

/**
 * JobCard component matching exact design specifications
 */
export const JobCard = memo(
  ({
    job,
    className,
    onBookmark,
    onApply,
    onViewDetails,
    variant = "list",
    isMobile = false,
  }: JobCardProps) => {
    const formatSalary = (salary: Job["salary"]) => {
      const { min, max } = salary;
      if (min && max) {
        return `${min} - ${max} triệu`;
      }
      if (min) {
        return `${min}+ triệu`;
      }
      return "Thỏa thuận";
    };

    // Desktop List View - Based on provided code structure
    if (variant === "list") {
      return (
        <Card
          className={cn(
            "w-full max-w-[600px] border-2 border-transparent hover:border-emerald-400 rounded-xl shadow-sm bg-white transition-all duration-300 cursor-pointer",
            className
          )}
          onClick={() => onViewDetails?.(job.id)}
        >
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              {/* Company Logo */}
              <div className="flex-shrink-0">
                <div className="w-16 h-16 rounded-xl border border-gray-200 bg-white flex items-center justify-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-lg">
                      {job.company.name.charAt(0)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Job Content */}
              <div className="flex-1 min-w-0">
                {/* Job Title */}
                <h3 className="text-[17px] font-semibold text-gray-900 leading-[1.4] mb-1 line-clamp-2">
                  {job.title}
                </h3>

                {/* Company Name */}
                <p className="text-[14px] text-gray-500 mb-3 line-clamp-1 uppercase tracking-wide">
                  {job.company.name}
                </p>

                {/* Location and Requirements */}
                <div className="flex items-center gap-3 mb-3">
                  <div className="flex items-center gap-1 text-[13px] text-gray-600">
                    <MapPin className="h-3 w-3" />
                    <span>{job.location}</span>
                  </div>
                  <span className="text-[13px] text-gray-600">
                    Không yêu cầu
                  </span>
                </div>

                {/* Tags */}
                <div className="flex items-center gap-2 flex-wrap">
                  <div className="bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-[13px] font-medium">
                    Direct Sales
                  </div>
                  <div className="bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-[13px] font-medium">
                    Tư vấn tài chính
                  </div>
                  <div className="bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-[13px] font-medium">
                    B2C
                  </div>
                  <div className="bg-blue-50 text-blue-600 px-3 py-1 rounded-full text-[13px] font-medium">
                    Bảo hiểm
                  </div>
                  <div className="bg-emerald-50 text-emerald-600 px-3 py-1 rounded-full text-[13px] font-medium">
                    +4
                  </div>
                </div>
              </div>

              {/* Right Side - Salary and Bookmark */}
              <div className="flex flex-col items-end gap-3">
                {/* Salary */}
                <div className="text-right">
                  <div className="text-2xl font-bold text-emerald-500 mb-1">
                    {formatSalary(job.salary)}
                  </div>
                  <div className="text-[13px] text-gray-500 flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>Đăng 1 tuần trước</span>
                  </div>
                </div>

                {/* Bookmark Heart */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-10 h-10 rounded-full bg-green-100 hover:bg-green-200 p-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    onBookmark?.(job.id);
                  }}
                >
                  <Heart
                    className={cn(
                      "w-5 h-5",
                      job.isBookmarked
                        ? "fill-green-600 text-green-600"
                        : "text-green-600"
                    )}
                  />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    }

    // Mobile Grid View - Based on provided code structure
    return (
      <Card
        className={cn(
          "w-full max-w-[600px] border border-gray-200 rounded-xl shadow-sm bg-white transition-all duration-300 cursor-pointer",
          className
        )}
        onClick={() => onViewDetails?.(job.id)}
      >
        <CardContent className={isMobile ? "p-4" : "p-6"}>
          <div className="flex items-start gap-4">
            {/* Company Logo */}
            <div className="flex-shrink-0">
              <div
                className={cn(
                  "rounded-xl border border-gray-200 bg-white flex items-center justify-center",
                  isMobile ? "w-12 h-12" : "w-16 h-16"
                )}
              >
                <div
                  className={cn(
                    "bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center",
                    isMobile ? "w-10 h-10" : "w-12 h-12"
                  )}
                >
                  <span
                    className={cn(
                      "text-white font-bold",
                      isMobile ? "text-sm" : "text-lg"
                    )}
                  >
                    {job.company.name.substring(0, 2).toUpperCase()}
                  </span>
                </div>
              </div>
            </div>

            {/* Job Content */}
            <div className="flex-1 min-w-0">
              {/* Job Title */}
              <h3
                className={cn(
                  "font-semibold text-gray-900 leading-[1.4] mb-1 line-clamp-2",
                  isMobile ? "text-[15px]" : "text-[17px]"
                )}
              >
                {job.title}
              </h3>

              {/* Company Name */}
              <p
                className={cn(
                  "text-gray-500 line-clamp-1 uppercase tracking-wide mb-3",
                  isMobile ? "text-[12px]" : "text-[14px]"
                )}
              >
                {job.company.name}
              </p>

              {/* Salary and Location */}
              <div className="flex items-center gap-3">
                <div
                  className={cn(
                    "bg-gray-100 text-gray-700 px-3 py-1.5 rounded-full font-medium",
                    isMobile ? "text-[12px]" : "text-[13px]"
                  )}
                >
                  {formatSalary(job.salary)}
                </div>
                <div
                  className={cn(
                    "bg-gray-100 text-gray-700 px-3 py-1.5 rounded-full font-medium flex items-center gap-1",
                    isMobile ? "text-[12px]" : "text-[13px]"
                  )}
                >
                  <MapPin className="h-3 w-3" />
                  {job.location}
                </div>
              </div>
            </div>

            {/* Bookmark Heart */}
            <div className="flex-shrink-0">
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "rounded-full bg-green-100 hover:bg-green-200 p-0",
                  isMobile ? "w-8 h-8" : "w-10 h-10"
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  onBookmark?.(job.id);
                }}
              >
                <Heart
                  className={cn(
                    job.isBookmarked
                      ? "fill-green-600 text-green-600"
                      : "text-green-600",
                    isMobile ? "w-4 h-4" : "w-5 h-5"
                  )}
                />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }
);

JobCard.displayName = "JobCard";
